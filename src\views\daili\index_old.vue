<template>
  <div class="daili-container">
    <el-form v-loading="loading"  ref="form" :model="form" label-width="100px">
      <el-form-item label="IP类型" prop="type">
        <el-radio-group @change="findCountry()" v-model="form.type" size="mini">
          <el-radio-button v-for="(item, key) in info.types" :label="item.value">{{ item.label }}</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <div style="color:#999;font-size: 12px;padding-left: 100px;margin-top: -15px;">
        <div v-for="(item, key) in info.types">
          <div v-if="item.value == 'dedicated' && form.type == 'dedicated'">{{ item.label }}：{{ item.memo }}</div>
          <div v-if="item.value == 'shared' && form.type == 'shared'">{{ item.label }}：{{ item.memo }}</div>
        </div>
      </div>
      <el-form-item label="IP版本" prop="type">
        <el-radio-group @change="findCountry()" v-model="form.bb" size="mini">
          <el-radio-button v-for="(item, key) in info.ips" :label="item.value">{{ item.label }}</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="国家" prop="type">
        <el-radio-group @change="getPrice()" v-model="form.guojia" size="mini">
          <el-radio style="width: 200px" v-for="(item, key) in info.countrys" :label="item.abbreviation_name">{{
            item.cn_name
            }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="数量" prop="type">
        <el-input-number size="mini" @change="getPrice()" v-model="form.num" :min="1" :max="2000"
          :step="1"></el-input-number>
        <div style="display: none;" @click="selectIp()" class="div_link">
          <div>选择IP</div>
        </div>
      </el-form-item>
      <el-form-item label="天数" prop="type">
        <el-radio-group @change="getPrice()" v-model="form.date" size="mini">
          <el-radio-button v-for="(item, key) in info.periods" :label="item.value">{{ item.label }}天</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="结算金额" prop="type">
        <span style="color: #f55906;font-size: 36px">{{ form.amount }}</span>元
      </el-form-item>
      <el-form-item>
        <el-button @click="createDlOrder()" type="primary" size="mini">立即下单</el-button>
      </el-form-item>
    </el-form>

    <el-dialog title="提示" :visible.sync="dialogVisible" width="30%" :before-close="handleClose">
      <span>这是一段信息</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import { findProxyHome, getPrice, createOrder,findProxyCountry } from '@/api/daili.js'
import { getToken } from '@/utils/auth'
import { MessageBox, Message } from 'element-ui'
export default {
  name: "index",
  data() {
    return {
      dialogVisible: false,
      loading: false,
      info: {
        countrys:[]
      },
      form: {
        type: "dedicated",
        bb: "4",
        guojia: "",
        num: 1,
        date: "30",
        amount: 0
      }
    }
  },
  methods: {
    findCountry(){
      setTimeout(() => {
        let params = {
            type_id: this.form.type,
            ip_version:this.form.bb
        };
        findProxyCountry(params).then(res => {
          this.form.guojia = "";
          this.$set(this.info, 'countrys', res.data.countrys)
          // this.info.countrys = res.data.countrys;
          console.log(this.info.countrys);
          this.loading = false;
          this.getPrice();
        }).catch(err => {
          this.loading = false;
        })
      }, 100);
    },
    selectIp() {
      this.dialogVisible = true;
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done();
        })
        .catch(_ => { });
    },
    getPrice() {
      let params = {
        type: this.form.type,
        ip_version: this.form.bb,
        country: this.form.guojia,
        quantity: this.form.num,
        period: this.form.date
      };
      if (!params.country) {
        return;
      }
      this.loading = true;
      getPrice(params).then(res => {
        console.log(res.data.amount);
        this.form.amount = res.data.amount || 0;
        this.loading = false;
      }).catch(err => {
        this.loading = false;
      })
    },
    createDlOrder() {
      let params = {
        type: this.form.type,
        ip_version: this.form.bb,
        country: this.form.guojia,
        quantity: this.form.num,
        period: this.form.date
      };
      if (!params.country) {
        return;
      }
      console.log("getToken", getToken());
      this.loading = true;
      createOrder(params).then(res => {
        this.loading = false;
        console.log(res);
        if (res.code != "200") {
          Message({
            message: res.msg,
            type: 'error',
            duration: 5 * 1000
          })
          return;
        }
        Message({
          message: res.msg,
          type: 'success',
          duration: 5 * 1000
        });
        setTimeout(() => {
          this.$router.push({ path: '/daili/dailiOrder' })
        }, 1000);
      }).catch(err => {
        console.log(err);
        this.loading = false;
      });

    }
  },
  mounted() {
    findProxyHome().then(res => {
      this.info = res.data;
      console.log(this.info);
      setTimeout(() => {
        this.getPrice();
        setTimeout(() => {
          this.findCountry();
        }, 100);
      }, 100);
    })
  }
}
</script>

<style lang="scss" scoped>
.daili {
  &-container {
    margin: 30px;
  }

  &-text {
    font-size: 30px;
    line-height: 46px;
  }
}

.div_link {
  text-decoration: underline;
  cursor: pointer;
  color: #409eff;
}
</style>
