<template>
  <el-table
      :data="tableData"
      style="width: 100%">
    <el-table-column
        label="交易时间"
        width="170"
        prop="add_time_format">
    </el-table-column>
    <el-table-column
        label="描述"
        prop="memo">
    </el-table-column>
  </el-table>
</template>

<script>

import { findMoneyList } from '@/api/order.js'
export default {
  data() {
    return {
      tableData: []
    }
  },
  mounted() {
    // /fensi/fensiDetail
    findMoneyList().then(res => {
      this.tableData = res.data
      console.log(this.tableData)
    })
  }
}
</script>

<style scoped>

</style>
