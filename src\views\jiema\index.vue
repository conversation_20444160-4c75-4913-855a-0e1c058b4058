<template>
  <el-table
      :data="tableData"
      @expand-change="(row) => {findSmsCountryByServer(row)}"
      expand-row-keys
      style="width: 100%">
    <el-table-column type="expand">
      <template slot-scope="props">
        <el-form label-position="right" class="demo-table-expand"  label-width="160px">
          <el-form-item label="验证方式">
            <el-radio-group size="mini"  v-model="form.type_id">
              <el-radio-button label="1">短信</el-radio-button>
              <!-- <el-radio-button label="1">号码</el-radio-button>
              <el-radio-button label="1">语音</el-radio-button> -->
            </el-radio-group>
          </el-form-item>
          <el-form-item label="国家">
            <el-select v-model="form.sms_channel_country_id" filterable placeholder="请选择" @change="countryChange"> 
              <el-option
                  v-for="item in countryList"
                  :key="item.fid"
                  :label="item.country_name"
                  :value="item.sms_channel_country_id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="运营商" >
            <el-select v-model="form.sms_operator_id" @change="getNewPrice()" filterable placeholder="请选择">
              <el-option
                  v-for="item in operatorList"
                    :key="item.fid"
                  :label="item.operator_name"
                  :value="item.fid"
                  >
              </el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="时间">
            <el-radio-group size="mini">
              <el-radio-button label="1">20分钟</el-radio-button>
              <el-radio-button label="1">40小时</el-radio-button>
              <el-radio-button label="1">12小时</el-radio-button>
              <el-radio-button label="1">1天</el-radio-button>
            </el-radio-group>
          </el-form-item> -->
          <el-form-item label="价格">
            {{form.price}}
          </el-form-item>
          <el-form-item>
            <el-button @click="createDlOrder(props.row)" type="primary" size="mini">立即下单</el-button>
          </el-form-item>
        </el-form>
      </template>
    </el-table-column>
    <el-table-column
        label="服务名"
        prop="server_name">
    </el-table-column>
    <el-table-column
        label="剩余总数"
        prop="total_cnt">
    </el-table-column>
  </el-table>
</template>

<script>
import { Message } from 'element-ui'
import { findSmsServer, findSmsCountryByServer, findSmsOperatorByCountry, getNewPrice,reqNumber} from '@/api/jiema.js'
export default {
  data() {
    return {
      form:{
        sms_channel_country_id:"",
        sms_channel_server_id:"",
        type_id:"1",
        sms_operator_id:"",
        price:0.0
      },
      tableData: [],
      countryList: [],
      operatorList: []
    }
  },
  methods: {
    countryChange(e){
      console.log(e);
      this.form.sms_operator_id ="";
      this.findSmsOperatorByCountry(e);
      this.getNewPrice();
    },    
    findSmsServer() {
      findSmsServer().then(res => {
        this.tableData = res.data
        this.tableData.forEach(v => {
          this.$set(v, 'sms_channel_server_id', '')
          this.$set(v, 'sms_channel_country_id', '')
          this.$set(v, 'price', '0.00')
          this.form.type_id = "1";
          this.form.sms_channel_country_id = "";

        })
      })
    },
    findSmsCountryByServer(row) {
      console.log(row)
      this.form.sms_channel_server_id = row.fid;
      this.countryList = [];
      this.form.sms_channel_country_id = "";
      this.form.sms_operator_id ="";
      findSmsCountryByServer({
        sms_channel_server_id: row.fid
      }).then(res => {
        this.countryList = res.data
        console.log(this.countryList)
      })
    },
    findSmsOperatorByCountry(sms_channel_country_id) {
      findSmsOperatorByCountry({
        sms_channel_country_id: sms_channel_country_id
      }).then(res => {
        this.operatorList = res.data
        console.log(this.tableData)
      })
    },
    getNewPrice() {

       const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

      getNewPrice({

        // sms_channel_country_id:"",
        // type_id:"1",
        // sms_operator_id:""

        sms_channel_country_id: this.form.sms_channel_country_id,
        sms_channel_server_id: this.form.sms_channel_server_id
      }).then(res => {
        // this.$set(row, 'price', '0.00');
        this.form.price = res.data;
        console.log(res);
                   loading.close();

      })
    },
    createDlOrder() {
      if(!this.form.sms_channel_country_id) {
        Message({
           message: "请选择国家",
           type: 'success',
           duration: 5 * 1000
         })
        return;
      }
      if(!this.form.sms_channel_server_id) {
        Message({
           message: "请选择服务",
           type: 'success',
           duration: 5 * 1000
         })
        return;
      }
      // if(!this.form.sms_operator_id) {
      //   Message({
      //      message: "请选择渠道",
      //      type: 'success',
      //      duration: 5 * 1000
      //    })
      //   return;
      // }
      if(!this.form.type_id) {
        Message({
           message: "请选择类型",
           type: 'success',
           duration: 5 * 1000
         })
        return;
      }

        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

      reqNumber(this.form).then(res => {
        console.log(res);
        if(res.code!="200") {
          Message({
           message: res.msg,
           type: 'error',
           duration: 5 * 1000
         });
         loading.close();
          return;
        }

        Message({
           message: res.msg,
           type: 'success',
           duration: 5 * 1000
         })
                 loading.close();

          setTimeout(() => {
            this.$router.push({ path: '/jiema/jiemaOrder' })
          }, 500);


      })

    },
  },
  mounted() {
    this.findSmsServer()
  }
}
</script>


<style>
.demo-table-expand {
  font-size: 0;
}
.demo-table-expand label {
  color: #99a9bf;
}
</style>
