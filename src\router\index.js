import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [{
      path: 'dashboard',
      name: 'SocialAccTrade',
      component: () => import('@/views/dashboard/index'),
      meta: { title: 'SocialAccTrade', icon: 'info' }
    }]
  },
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register/index'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  },
  {
    path: '/daili',
    component: Layout,
    redirect: '/example/index',
    name: 'daili',
    meta: { title: 'Acting', icon: 'connection' },
    children: [
      {
        path: 'daili',
        name: 'daili',
        component: () => import('@/views/daili/index'),
        meta: { title: 'Agent Products', icon: 'position' }
      },
      {
        path: 'dailiOrder',
        name: 'dailiOrder',
        component: () => import('@/views/daili/order'),
        meta: { title: 'Proxy Records', icon: 'goods' }
      }
    ]
  },
  {
    path: '/fensi',
    component: Layout,
    redirect: '/fensi/index',
    name: 'fensi',
    meta: { title: 'Brush data', icon: 'pie-chart' },
    children: [
      {
        path: 'fensi',
        name: 'fensi',
        component: () => import('@/views/fensi/index'),
        meta: { title: '刷数据产品', icon: 'pie-chart' }
      },
      {
        path: 'fensiOrder',
        name: 'fensiOrder',
        component: () => import('@/views/fensi/order'),
        meta: { title: '刷数据记录', icon: 'goods' }
      },
      {
        path: 'fensiDetail',
        name: 'fensiDetail',
        component: () => import('@/views/fensi/fensiDetail'),
        meta: { title: '明细', icon: 'goods' },
        hidden: true
      }
    ]
  },
  {
    path: '/jiema',
    component: Layout,
    redirect: '/jiema/index',
    name: 'jiema',
    meta: { title: 'Code', icon: 'mobile' },
    children: [
      {
        path: 'jiema',
        name: 'jiema',
        component: () => import('@/views/jiema/index'),
        meta: { title: 'Products', icon: 'mobile' }
      },
      {
        path: 'jiemaOrder',
        name: 'jiemaOrder',
        component: () => import('@/views/jiema/order'),
        meta: { title: 'Record', icon: 'goods' }
      }
    ]
  },
  {
    path: '/qianbao',
    component: Layout,
    redirect: '/qianbao',
    children: [{
      path: 'qianbao',
      name: 'My wallet',
      component: () => import('@/views/qianbao/index'),
      meta: { title: 'My wallet', icon: 'bank-card' }
    }]
  },
  {
    path: '/order',
    component: Layout,
    redirect: '/order',
    children: [{
      path: 'order',
      name: 'My Orders',
      component: () => import('@/views/order/index'),
      meta: { title: 'My Orders', icon: 's-order' }
    }]
  },
  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-*********
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
