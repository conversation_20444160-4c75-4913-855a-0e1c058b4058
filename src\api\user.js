import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/user/login',
    method: 'post',
    data
  })
}

export function getInfo(token) {
  return request({
    url: '/user/info',
    method: 'get',
    params: { token }
  })
}

export function getMemberInfo(token) {
  return request({
    url: '/sms/getMemberInfo',
    method: 'get',
    params: { token }
  })
}
export function getAlipay(token) {
  return request({
    url: '/sms/getAlipay',
    method: 'get',
    params: { token }
  })
}
export function getOrderAmt(params) {
  return request({
    url: '/sms/getOrderAmt',
    method: 'get',
    params: params
  })
}

export function logout() {
  return request({
    url: '/user/logout',
    method: 'post'
  })
}

export function getVerificationCode() {
  return request({
    url: '/user/getVerificationCode',
    method: 'post'
  })
}
