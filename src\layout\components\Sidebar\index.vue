<template>
  <div class="sidebar-wrapper">
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="false"
        mode="vertical"
        class="sidebar-menu"
      >
        <!-- <router-link to="/dashboard" class="menu-item-link">
          <div class="menu-item home-menu" :class="{ 'is-active': $route.path === '/dashboard' }">
            <i class="el-icon-s-home"></i>
            <span>首页</span>
          </div>
        </router-link> -->
        
        <div 
          v-for="(route, index) in routes" 
          :key="index"
          v-if="!route.hidden && route.children"
        >
          <template v-if="route.children.length === 1">
            <router-link :to="resolvePath(route.path, route.children[0].path)" class="menu-item-link">
              <div class="menu-item" :class="{
                'is-active': isActive(route),
                'socialacctrade-menu': route.children[0].meta.title === 'SocialAccTrade'
              }">
                <i :class="'el-icon-' + (route.children[0].meta && route.children[0].meta.icon)"></i>
                <span>{{ route.children[0].meta.title }}</span>
              </div>
            </router-link>
          </template>
          
          <el-submenu v-else :index="route.path" popper-append-to-body>
            <template slot="title">
              <i :class="'el-icon-' + (route.meta && route.meta.icon)"></i>
              <span>{{ route.meta.title }}</span>
            </template>
            
            <router-link 
              v-for="child in route.children" 
              :key="child.path"
              :to="resolvePath(route.path, child.path)"
              class="submenu-item-link"
            >
              <el-menu-item :index="resolvePath(route.path, child.path)">
                <span>{{ child.meta.title }}</span>
              </el-menu-item>
            </router-link>
          </el-submenu>
        </div>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import path from 'path'
import variables from '@/styles/variables.scss'

export default {
  computed: {
    ...mapGetters([
      'sidebar'
    ]),
    routes() {
      return this.$router.options.routes
    },
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    variables() {
      return variables
    }
  },
  methods: {
    resolvePath(basePath, routePath) {
      if (path.isAbsolute(routePath)) {
        return routePath
      }
      return path.resolve(basePath, routePath)
    },
    isActive(route) {
      if (route.children && route.children.length === 1) {
        const routePath = this.resolvePath(route.path, route.children[0].path)
        return this.$route.path === routePath || this.$route.path.indexOf(routePath + '/') === 0
      }
      return false
    }
  }
}
</script>

<style lang="scss" scoped>
.sidebar-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .scrollbar-wrapper {
    flex: 1;
    overflow-x: hidden !important;
  }
  
  .menu-item-link {
    text-decoration: none;
    display: block;

    .menu-item {
      height: 42px;
      padding: 0 20px;
      color: rgba(255,255,255,0.7);
      display: flex;
      align-items: center;
      border-radius: 0;

      i {
        width: 20px;
        height: 20px;
        margin-right: 12px;
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
      }

      span {
        flex: 1;
        font-size: 14px;
        font-weight: 500;
        line-height: 1.4;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      // SocialAccTrade 菜单特殊样式
      &[data-title="SocialAccTrade"] {
        position: relative;
        color: #fff;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 20px;
          right: 20px;
          bottom: 0;
          background-color: #083A4C;
          border-radius: 8px;
          z-index: -1;
        }
      }

      // 首页菜单样式
      &.home-menu {
        &.is-active {
          color: #fff;
          position: relative;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 20px;
            right: 20px;
            bottom: 0;
            background-color: #083A4C;
            border-radius: 8px;
            z-index: -1;
          }
        }


      }

      // 父菜单激活样式
      &.is-active:not(.home-menu) {
        color: #fff;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 20px;
          right: 20px;
          bottom: 0;
          background-color: #083A4C;
          border-radius: 8px;
          z-index: -1;
        }
      }



      .submenu-arrow {
        margin-left: auto;
        margin-right: 0;
        font-size: 12px;
        width: 16px;
        height: 16px;
      }
    }
  }
  
  .submenu-item-link {
    text-decoration: none;
  }
}

>>>.el-scrollbar__wrap {
  overflow-x: hidden !important;
}

>>>.el-menu {
  border-right: none;
  padding: 10px 20px;
}

.sidebar-menu {
  padding: 15px 0;
}

>>>.el-submenu {
  .el-submenu__title {
    height: 42px !important;
    line-height: 42px !important;
    padding: 0 20px !important;
    color: rgba(255,255,255,0.7) !important;

    i {
      width: 20px;
      height: 20px;
      margin-right: 12px !important;
      font-size: 18px !important;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }

    span {
      font-size: 14px;
      font-weight: 500;
    }



    &.is-active {
      color: #fff !important;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 20px;
        right: 20px;
        bottom: 0;
        background-color: #083A4C;
        border-radius: 8px;
        z-index: -1;
      }
    }

    &.is-opened {
      color: #fff !important;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 20px;
        right: 20px;
        bottom: 0;
        background-color: #083A4C;
        border-radius: 8px;
        z-index: -1;
      }
    }
  }

  .el-menu-item {
    height: 40px !important; // 增加二级菜单高度
    line-height: 40px !important;
    padding: 0 20px 0 40px !important; // 左侧缩进体现层级
    color: rgba(255,255,255,0.7) !important;
    font-size: 13px !important; // 稍小的字体区分层级
    font-weight: 400 !important;


    &.is-active {
      color: #fff !important;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 3px;
        left: 32px;
        right: 16px;
        bottom: 3px;
        background-color: #0a4a5c;
        border-radius: 6px;
        z-index: -1;
      }
    }

    &[aria-selected="true"] {
      color: #fff !important;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 3px;
        left: 32px;
        right: 16px;
        bottom: 3px;
        background-color: #0a4a5c;
        border-radius: 6px;
        z-index: -1;
      }
    }
  }
}
</style>


