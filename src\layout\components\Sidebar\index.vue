<template>
  <div class="sidebar-wrapper">
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="false"
        mode="vertical"
      >
        <router-link to="/dashboard" class="menu-item-link">
          <div class="menu-item home-menu" :class="{ 'is-active': $route.path === '/dashboard' }">
            <i class="el-icon-s-home"></i>
            <span>首页</span>
          </div>
        </router-link>
        
        <div 
          v-for="(route, index) in routes" 
          :key="index"
          v-if="!route.hidden && route.children"
        >
          <template v-if="route.children.length === 1">
            <router-link :to="resolvePath(route.path, route.children[0].path)" class="menu-item-link">
              <div class="menu-item" :class="{ 'is-active': isActive(route) }">
                <i :class="'el-icon-' + (route.children[0].meta && route.children[0].meta.icon)"></i>
                <span>{{ route.children[0].meta.title }}</span>
                <i v-if="route.children.length > 0" class="el-icon-arrow-down submenu-arrow"></i>
              </div>
            </router-link>
          </template>
          
          <el-submenu v-else :index="route.path" popper-append-to-body>
            <template slot="title">
              <i :class="'el-icon-' + (route.meta && route.meta.icon)"></i>
              <span>{{ route.meta.title }}</span>
            </template>
            
            <router-link 
              v-for="child in route.children" 
              :key="child.path"
              :to="resolvePath(route.path, child.path)"
              class="submenu-item-link"
            >
              <el-menu-item :index="resolvePath(route.path, child.path)">
                <span>{{ child.meta.title }}</span>
              </el-menu-item>
            </router-link>
          </el-submenu>
        </div>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import path from 'path'
import variables from '@/styles/variables.scss'

export default {
  computed: {
    ...mapGetters([
      'sidebar'
    ]),
    routes() {
      return this.$router.options.routes
    },
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    variables() {
      return variables
    }
  },
  methods: {
    resolvePath(basePath, routePath) {
      if (path.isAbsolute(routePath)) {
        return routePath
      }
      return path.resolve(basePath, routePath)
    },
    isActive(route) {
      if (route.children && route.children.length === 1) {
        const routePath = this.resolvePath(route.path, route.children[0].path)
        return this.$route.path.indexOf(routePath) === 0
      }
      return false
    }
  }
}
</script>

<style lang="scss" scoped>
.sidebar-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .scrollbar-wrapper {
    flex: 1;
    overflow-x: hidden !important;
  }
  
  .menu-item-link {
    text-decoration: none;
    display: block;

    .menu-item {
      height: 50px;
      padding: 0 20px;
      color: rgba(255,255,255,0.7); // 更浅的未激活颜色
      display: flex;
      align-items: center;
      transition: all 0.3s ease;
      border-radius: 0;

      i {
        width: 20px;
        height: 20px;
        margin-right: 12px;
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
      }

      span {
        flex: 1;
        font-size: 14px;
        font-weight: 500;
        line-height: 1.4;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      // 首页菜单样式
      &.home-menu {
        &.is-active {
          color: #fff;
          background: linear-gradient(to right, transparent 8px, #083A4C 8px, #083A4C calc(100% - 8px), transparent calc(100% - 8px));
          margin: 0 8px;
          border-radius: 6px;
        }

        &:hover {
          background: linear-gradient(to right, transparent 8px, #083A4C 8px, #083A4C calc(100% - 8px), transparent calc(100% - 8px));
          margin: 0 8px;
          border-radius: 6px;
          color: #fff;
        }
      }

      // 父菜单激活样式
      &.is-active:not(.home-menu) {
        color: #fff;
        background: linear-gradient(to right, transparent 8px, #083A4C 8px, #083A4C calc(100% - 8px), transparent calc(100% - 8px));
        margin: 0 8px;
        border-radius: 6px;
      }

      &:hover:not(.home-menu) {
        background: linear-gradient(to right, transparent 8px, #083A4C 8px, #083A4C calc(100% - 8px), transparent calc(100% - 8px));
        margin: 0 8px;
        border-radius: 6px;
        color: #fff;
      }

      .submenu-arrow {
        margin-left: auto;
        margin-right: 0;
        font-size: 12px;
        width: 16px;
        height: 16px;
        transition: transform 0.3s ease;
      }
    }
  }
  
  .submenu-item-link {
    text-decoration: none;
  }
}

:deep(.el-scrollbar__wrap) {
  overflow-x: hidden !important;
}

:deep(.el-menu) {
  border-right: none;
}

// Element UI 子菜单样式优化
:deep(.el-submenu) {
  .el-submenu__title {
    height: 50px !important;
    line-height: 50px !important;
    padding: 0 20px !important;
    color: rgba(255,255,255,0.7) !important; // 更浅的未激活颜色
    transition: all 0.3s ease !important;

    i {
      width: 20px;
      height: 20px;
      margin-right: 12px !important;
      font-size: 18px !important;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }

    span {
      font-size: 14px;
      font-weight: 500;
    }

    &:hover {
      background: linear-gradient(to right, transparent 8px, #083A4C 8px, #083A4C calc(100% - 8px), transparent calc(100% - 8px)) !important;
      margin: 0 8px !important;
      border-radius: 6px !important;
      color: #fff !important;
    }

    &.is-active {
      background: linear-gradient(to right, transparent 8px, #083A4C 8px, #083A4C calc(100% - 8px), transparent calc(100% - 8px)) !important;
      margin: 0 8px !important;
      border-radius: 6px !important;
      color: #fff !important;
    }
  }

  .el-menu-item {
    height: 45px !important;
    line-height: 45px !important;
    padding: 0 20px 0 52px !important; // 左侧缩进对齐图标
    color: rgba(255,255,255,0.7) !important; // 更浅的未激活颜色
    font-size: 14px;
    transition: all 0.3s ease !important;

    &:hover {
      background: linear-gradient(to right, transparent 16px, #0a4a5c 16px, #0a4a5c calc(100% - 8px), transparent calc(100% - 8px)) !important;
      margin: 0 8px !important;
      border-radius: 6px !important;
      color: #fff !important;
    }

    &.is-active {
      color: #fff !important;
      background: linear-gradient(to right, transparent 16px, #0a4a5c 16px, #0a4a5c calc(100% - 8px), transparent calc(100% - 8px)) !important;
      margin: 0 8px !important;
      border-radius: 6px !important;
    }
  }
}
</style>


