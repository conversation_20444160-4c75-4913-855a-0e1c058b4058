<template>
  <div class="login-container">
    <div class="login-form-section">
      <div class="login-form-wrapper">
        <div class="login-header">
          <!-- <span class="wave-emoji">👋</span> -->
          
          <h1 class="title">欢迎登录账号 <img src="@/assets/wave.png" alt="wave-emoji" class="wave-emoji" /></h1>
          
          <p class="subtitle">新的一天来了，这是你需要面对的日子，登录并开始你的旅程吧。</p>
        </div>
        
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" auto-complete="on">
          <div class="form-item-label">Email</div>
          <el-form-item prop="email">
            <el-input v-model="loginForm.email" placeholder="<EMAIL>" type="text" />
          </el-form-item>
          
          <div class="form-item-label">Password</div>
          <el-form-item prop="password">
            <el-input v-model="loginForm.password" placeholder="At least 8 characters" type="password" />
          </el-form-item>
          
          <div class="forgot-password">
            <a href="javascript:void(0)">忘记密码?</a>
          </div>
          
          <el-button :loading="loading" type="primary" class="submit-button" @click.native.prevent="handleLogin">
            登录
          </el-button>
          
          <div class="divider-text">
            <span>Or</span>
          </div>
          
          <div class="social-login">
            <el-button class="social-button google-button">
              <img src="@/assets/google.png" alt="Google" class="social-icon" />
              Sign in with Google
            </el-button>
            
            <el-button class="social-button facebook-button">
              <img src="@/assets/facebook.png" alt="Facebook" class="social-icon" />
              Sign in with Facebook
            </el-button>
          </div>
          
          <div class="register-link">
            你没有账户? <router-link to="/register">注册</router-link>
          </div>
        </el-form>
      </div>
      
      <div class="footer">
        © {{ new Date().getFullYear() }} 版权所有
      </div>
    </div>
    
    <!-- <div class="background-section"></div> -->
    <div class="background-container">
      <div class="background-section"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Login',
  data() {
    const validateEmail = (rule, value, callback) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(value)) {
        callback(new Error('请输入有效的邮箱地址'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length < 8) {
        callback(new Error('密码不能少于8个字符'))
      } else {
        callback()
      }
    }
    return {
      loginForm: {
        email: '',
        password: ''
      },
      loginRules: {
        email: [{ required: true, trigger: 'blur', validator: validateEmail }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }]
      },
      loading: false
    }
  },
  methods: {
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          // 实现登录逻辑
          setTimeout(() => {
            this.loading = false
            this.$router.push('/')
          }, 1500)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  display: flex;
  min-height: 100vh;
  width: 100%;
  
  .login-form-section {
    width: 55%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 40px 24px;
    
    .login-form-wrapper {
      width: 100%;
      max-width: 380px;
      margin: 100px auto 0;
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      
      .login-header {
        margin-bottom: 30px;
        
        .title {
          font-size: 28px;
          font-weight: 600;
          color: #0d3b49;
          margin-bottom: 10px;
          display: flex;
          align-items: center;
          
          .wave-emoji {
            display: inline-block;
            height: 45px;
            width: auto;
            margin-left: 5px;
            // vertical-align: middle;
            margin-bottom: 0;
            margin-top: 0;
            position: relative;
            top: -1px;
            animation: wave 1.5s infinite;
            transform-origin: 70% 70%;
          }
          
          @keyframes wave {
            0% { transform: rotate(0deg); }
            10% { transform: rotate(14deg); }
            20% { transform: rotate(-8deg); }
            30% { transform: rotate(14deg); }
            40% { transform: rotate(-4deg); }
            50% { transform: rotate(10deg); }
            60% { transform: rotate(0deg); }
            100% { transform: rotate(0deg); }
          }
        }
        
        .subtitle {
          font-size: 14px;
          color: #666;
          line-height: 1.5;
        }
      }
      
      .login-form {
        .form-item-label {
          font-weight: 500;
          font-size: 14px;
          color: #333;
          margin-bottom: 8px;
        }
        
        .forgot-password {
          text-align: right;
          margin: -5px 0 15px;
          
          a {
            font-size: 13px;
            color: #616465;
            text-decoration: underline;
            
            &:hover {
              text-decoration: underline;
            }
          }
        }
        
        .submit-button {
          width: 100%;
          height: 44px;
          background-color: #0d3b49;
          border-color: #0d3b49;
          font-size: 16px;
          font-weight: 500;
          
          &:hover, &:focus {
            background-color: #165a6d;
            border-color: #165a6d;
          }
        }
        
        .divider-text {
          text-align: center;
          margin: 40px 0 25px 0;
          
          span {
            padding: 0 10px;
            color: #999;
            font-size: 14px;
          }
        }
        
        .social-login {
          margin-bottom: 20px;
          display: flex;
          flex-direction: column;
          gap: 12px;
          
          .social-button {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 44px;
            border: 1px solid #F3FAFA;
            background-color: #F3FAFA;
            color: #999;
            font-size: 12px;
            margin: 0;
            
            &:hover {
              background-color: #f5f5f5;
            }
            
            .social-icon {
              width: 20px;
              height: 20px;
              margin-right: 10px;
              position: relative;
              top: -1px; /* 微调图标位置 */
              vertical-align: middle;
            }
          }
        }
        
        .register-link {
          text-align: center;
          margin-top: 40px;
          font-size: 12px;
          color: #838C9A ;
          
          a {
            color: #0d3b49;
            text-decoration: none;
            font-weight: 500;
            
            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
    }
    
    .footer {
      width: 100%;
      text-align: center;
      color: #999;
      font-size: 12px;
      margin-top: 20px;
    }
  }
  
  .background-container {
    width: 45%;
    overflow: hidden;
    padding: 26px 65px 27px 0;
    .background-section {
      width: 100%;
      height: 100%;
      background-image: url('~@/assets/background.png');
      background-size: cover;
      background-position: center;
      border-radius: 1%;
    }
  }
}
</style>