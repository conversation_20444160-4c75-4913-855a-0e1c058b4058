<template>
  <div style="padding-top:20px">
    <el-form v-loading="loading" ref="form" label-width="100px">
      <el-form-item label="钱包余额" prop="type">
        <span style="font-size: 24px">¥</span><span style="font-size: 40px">{{memberInfo.amount || 0}}</span>
      </el-form-item>
      <el-form-item label="充值金额" prop="type">
        <el-input-number v-model="chongzhi" :min="50" size="mini" :max="99999" label="充值金额"></el-input-number>
      </el-form-item>
      <div style="color:#999;font-size: 12px;padding-left: 100px;margin-top: -15px;">
        <div>最低充值50元</div>
      </div>
      <el-form-item label="充值方式" prop="type">
        <el-radio-group size="mini" v-model="pay_style">
          <el-radio-button label="1">支付宝</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="type">
        <el-button  type="primary" @click="cz()" size="mini">立即充值</el-button>
      </el-form-item>
    </el-form>


    <el-dialog
      title="充值"
      :visible.sync="centerDialogVisible"
      width="450px"
      center>
      <div style="width: 100%;justify-content: center;align-items: center;display: flex;flex-direction: column;">
        <div>
          <img :src="imgUrl + pay_info.para_value_cn" alt="" style="height: 200px;width: 200px;">
        </div>
        <div style="margin:10px;font-size: 20px;">
          金额：{{ cz_amt || 0 }}
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="centerDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="ok()">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import { getMemberInfo,getAlipay,getOrderAmt} from '@/api/user.js'
export default {
name: "index",
  data() {
    return {
      imgUrl:"http://*************/home/",
      centerDialogVisible:false,
      memberInfo:{},
      pay_info:{},
      pay_style:"1",
      yue: 0,
      chongzhi: 50,
      cz_amt:0,
      loading: false
    }
  },
  methods:{
    ok(){
      this.load();
      this.centerDialogVisible = false;
    },
    cz(){
      this.centerDialogVisible = true;
      getOrderAmt({amt:this.chongzhi}).then(res => {
        // console.log(res.data.order_amount);
        this.cz_amt = res.data.order_amount;
      });
    },
    load(){
       const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        setTimeout(() => {
          getMemberInfo().then(res => {
           this.memberInfo = res.data;
           loading.close();
         });
          getAlipay().then(res => {
            this.pay_info = res.data;
            console.log(this.pay_info);
         });
        }, 500);

    }
  },
  mounted() {
    this.load();
  }
}
</script>

<style scoped>

</style>
