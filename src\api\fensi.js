import request from '@/utils/request'

export function findAllfans(params) {
    return request({
        url: '/fans/findAllfans',
        method: 'get',
        params
    })
}
export function findMyFansOrders(params) {
    return request({
        url: '/fans/findMyFansOrders',
        method: 'get',
        params
    })
}
export function getSmsFansConfigByAddConfig(params) {
    return request({
        url: '/fans/getSmsFansConfigByAddConfig',
        method: 'get',
        params
    })
}
export function reqFans(params) {
    return request({
        url: '/fans/reqFans',
        method: 'post',
        params
    })
}
export function reqComments(params) {
    return request({
        url: '/fans/reqComments',
        method: 'post',
        params
    })
}
export function reqSubscriptions(params) {
    return request({
        url: '/fans/reqSubscriptions',
        method: 'post',
        params
    })
}
export function reqWebTraffic(params) {
    return request({
        url: '/fans/reqWebTraffic',
        method: 'post',
        params
    })
}
export function getFansPrice(params) {
    return request({
        url: '/fans/getFansPrice',
        method: 'post',
        params
    })
}
