// sidebar
$menuText: rgba(255,255,255,0.9);
$menuActiveText: #fff;
$subMenuActiveText: #fff;

$menuBg: #051529;
$menuHover: #083A4C;

$subMenuBg: #051529;
$subMenuHover: #083A4C;

$sideBarWidth: 200px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}

