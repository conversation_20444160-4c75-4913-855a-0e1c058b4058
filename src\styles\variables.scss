// sidebar
$menuText: rgba(255,255,255,0.7);
$menuActiveText: #fff;
$subMenuActiveText: #fff;

$menuBg: #051529;
$menuHover: #083A4C;
$menuActiveParent: #083A4C;
$menuActiveSub: #0a4a5c;

$subMenuBg: #051529;
$subMenuHover: #0a4a5c;

$sideBarWidth: 200px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  menuActiveParent: $menuActiveParent;
  menuActiveSub: $menuActiveSub;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}

