<template>
  <div class="proxy-records-container">
    <div class="search-bar">
      <el-input
        placeholder="Please enter content"
        prefix-icon="el-icon-search"
        v-model="searchQuery"
        class="search-input"
      ></el-input>
      
      <el-date-picker
        v-model="dateValue"
        type="date"
        placeholder="Select Date"
        class="date-picker"
      ></el-date-picker>
    </div>
    
    <el-table 
      :data="tableData" 
      v-loading="loading"
      class="proxy-table"
      height="500px"
    >
      <el-table-column prop="id" label="代理ID" min-width="200" show-overflow-tooltip align="center"></el-table-column>
      <el-table-column prop="ip_version" label="IP类型" min-width="200" show-overflow-tooltip align="center"></el-table-column>
      <el-table-column prop="country_name" label="国家" min-width="200" show-overflow-tooltip align="center"></el-table-column>
      
      <el-table-column label="代理信息" min-width="350" show-overflow-tooltip>
        <template slot-scope="scope">
          <div class="proxy-info">
            <div class="ip-address">{{ scope.row.ip }}</div>
            <div class="credentials">
              <span class="label">userName: </span>
              <span class="value">{{ scope.row.username }}</span>
              <span class="label">password: </span>
              <span class="value">{{ scope.row.password }}</span>
            </div>
            <div class="ports">
              <span class="label">HTTP/SOCKS5 Port: </span>
              <span class="value">{{ scope.row.port_http }}/{{ scope.row.port_socks5 }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="date_end_format" label="截止日期" show-overflow-tooltip min-width="200" align="center"></el-table-column>
      
      <el-table-column label="操作" width="80" align="center">
        <template slot-scope="scope">
          <el-button 
            type="text" 
            class="renew-button"
            @click="xdOrder(scope.row)"
          >
            续订
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <span class="total-info">共 {{ total }} 条数据</span>
      
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        :page-size="pageSize"
        layout="prev, pager, next, jumper"
        :total="total"
        background
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { findMyProxy, xdMyProxy } from '@/api/daili.js'

export default {
  name: "ProxyRecords",
  data() {
    return {
      loading: false,
      tableData: [],
      searchQuery: '',
      dateValue: '',
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  },
  watch: {
    searchQuery: function(val) {
      if (this.timeout) clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        this.currentPage = 1;
        this.load();
      }, 500);
    },
    dateValue: function() {
      this.currentPage = 1;
      this.load();
    }
  },
  mounted() {
    // this.load();
  },
  methods: {
    load() {
      this.loading = true;
      findMyProxy({
        page: this.currentPage,
        query: this.searchQuery,
        date: this.dateValue ? this.formatDate(this.dateValue) : ''
      }).then(res => {
        this.loading = false;
        this.tableData = res.data || [];
        this.total = this.tableData.length; // 模拟总数据量
      }).catch(() => {
        this.loading = false;
      });
    },
    formatDate(date) {
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.load();
    },
    xdOrder(row) {
      this.$confirm('您确定续订吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        xdMyProxy(row).then(res => {
          this.loading = false;
          this.$message({
            type: 'success',
            message: res.msg || '续订成功!'
          });
          this.load();
        }).catch(() => {
          this.loading = false;
        });
      }).catch(() => {
        // 取消续订操作
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.proxy-records-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  height: calc(100vh - 84px);
  display: flex;
  flex-direction: column;
  
  .search-bar {
    display: flex;
    margin-bottom: 20px;
    
    .search-input {
      width: 220px;
      margin-right: 15px;
    }
    
    .date-picker {
      width: 220px;
    }
  }
  
  .proxy-table {
    flex: 1;
    margin-bottom: 20px;
  }
  
  .proxy-info {
    padding: 5px 0;
    
    .ip-address {
      font-weight: 500;
      margin-bottom: 5px;
    }
    
    .credentials, .ports {
      margin-bottom: 3px;
      font-size: 13px;
      
      .label {
        color: #909399;
        margin-right: 5px;
      }
      
      .value {
        margin-right: 15px;
      }
    }
  }
  
  .renew-button {
    color: #0d3b49;
    font-weight: 500;
    
    &:hover {
      color: #165a6d;
      text-decoration: underline;
    }
  }
  
  .pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .total-info {
      color: #606266;
      font-size: 13px;
    }
  }
}

>>>.el-table {
  font-size: 13px;
  
  th {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 500;
  }
}

>>>.el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: #0d3b49;
}
</style>