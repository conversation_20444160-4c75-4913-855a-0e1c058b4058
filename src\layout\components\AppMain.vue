<template>
  <section class="app-main">
    <!-- 面包屑导航 -->
    <div class="breadcrumb-container">
      <breadcrumb />
    </div>

    <!-- 页面内容 -->
    <div class="page-content">
      <transition name="fade-transform" mode="out-in">
        <router-view :key="key" />
      </transition>
    </div>
  </section>
</template>

<script>
import Breadcrumb from '@/components/Breadcrumb'

export default {
  name: 'AppMain',
  components: {
    Breadcrumb
  },
  computed: {
    key() {
      return this.$route.path
    }
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;

  .breadcrumb-container {
    background: #fff;
    padding: 0 20px;
    border-bottom: 1px solid #e8eaec;
    height: 50px;
    display: flex;
    align-items: center;
    flex-shrink: 0;
  }

  .page-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>



