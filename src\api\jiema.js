
import request from '@/utils/request'

export function findSmsServer(params) {
    return request({
        url: '/sms/findSmsServer',
        method: 'get',
        params
    })
}



export function findSmsCountryByServer(params) {
    return request({
        url: '/sms/findSmsCountryByServer',
        method: 'get',
        params
    })
}

export function findSmsOperatorByCountry(params) {
    return request({
        url: '/sms/findSmsOperatorByCountry',
        method: 'get',
        params
    })
}

export function getNewPrice(params) {
    return request({
        url: '/sms/getNewPrice',
        method: 'get',
        params
    })
}


export function findMySmsOrders(params) {
    return request({
        url: '/sms/findMySmsOrders',
        method: 'get',
        params
    })
}


export function reqNumber(params) {
    return request({
        url: '/sms/reqNumber',
        method: 'get',
        params
    })
}

