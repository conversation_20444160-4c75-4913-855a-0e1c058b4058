<template>
  <div class="daili-container">
    <el-table :data="tableData" style="width: 100%" v-loading="loading">
      <el-table-column prop="id" label="代理ID" width="180">
      </el-table-column>
      <el-table-column prop="ip_version" label="IP类型" width="100">
      </el-table-column>
      <el-table-column prop="country_name" label="国家" width="100">
      </el-table-column>
      <el-table-column prop="name" label="代理信息" width="430">
        <template slot-scope="scope">
          <div>
            <div>{{ scope.row.ip }}</div>
            <div>
              <span class="label">usernName：</span>
              {{ scope.row.username }}
              <span class="label">password：</span>
              {{ scope.row.password }}
            </div>
            <div>
              <span class="label">HTTP/SOCKS5 Port: </span>
              {{ scope.row.port_http }}/{{ scope.row.port_socks5 }}
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="date_end_format" label="截止日期" width="160">
      </el-table-column>
      <!-- <el-table-column prop="address" label="已使用流量" width="100">
      </el-table-column> -->
      <el-table-column prop="mod" label="操作" width="100">
        <template slot-scope="scope">
          <div>
            <div class="div_link" @click="xdOrder(scope.row)">续订</div>
          </div>
        </template>

      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { findMyProxy, xdMyProxy } from '@/api/daili.js'

export default {
  name: "index",
  data() {
    return {
      loading: false,
      tableData: []
    }
  },
  methods: {
    load() {
      this.loading = true;
      findMyProxy().then(res => {
        this.loading = false;
        console.log(res)
        this.tableData = res.data;
      });
    },
    xdOrder(row) {

      this.$confirm('您确定续订吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {


        this.loading = true;
        xdMyProxy(row).then(res => {
          this.loading = false;
          this.$message({
            type: 'success',
            message: res.msg || '续订成功!'
          });
          this.load();

        }).catch(()=>{
          this.loading = false;
        });



      });


    }
  },
  mounted() {
    this.load();
  }
}
</script>

<style lang="scss" scoped>
.daili {
  &-container {
    margin: 30px;
  }

  &-text {
    font-size: 30px;
    line-height: 46px;
  }
}

.label {
  color: #999;
}

.div_link {
  text-decoration: underline;
  cursor: pointer;
  color: #409eff;
}
</style>
