<template>
  <el-container :loading="loading">
    <div class="daili-container">
      <div class="left">
        <div class="items">
          <div class="label">类别</div>
          <div class="value">
            <el-select style="width:100%" @change="changeType" v-model="type_id" placeholder="请选择">
              <el-option v-for="item in types" :key="item.fid" :label="item.service_id + ' / ' + item.category"
                :value="item.fid">
              </el-option>
            </el-select>
          </div>
        </div>

        <div class="items">
          <div class="label">服务</div>
          <div class="value">
            <el-select style="width:100%" @change="changeService" v-model="server_id" placeholder="请选择">
              <el-option v-for="item in services" :key="item.fid" :label="item.service_id + ' / ' + item.name" :value="item.fid">
              </el-option>
            </el-select>
          </div>
        </div>

        <div class="items">
          <div class="label">描述</div>
          <div class="value">
            <p v-html="current_service_info.select_long_json"></p>
          </div>
        </div>

        <div class="items">
          <div class="label">链接</div>
          <div class="value"><el-input v-model="page.link" placeholder="请输入内容"></el-input>
          </div>
        </div>

        <div class="items">
          <div class="label">数量</div>
          <div class="value"><el-input type="number" @change="changeQuantity" v-model="page.quantity" placeholder="请输入内容"></el-input>
          </div>
        </div>

        <div class="items">
          <div class="label">最小订购</div>
          <div class="value">{{ current_service_info.min || 0 }}
          </div>
        </div>

        <div class="items" v-if="current_service_info.type == 'Custom Comments'">
          <div class="label">评论内容</div>
          <div class="value"><el-input v-model="page.comments" type="textarea" placeholder="请输入评论内容"></el-input>
          </div>
        </div>

        <div class="items">
          <div class="label">费用</div>
          <div class="value" style="color: #f55906;
    font-size: 36px;">
            {{ current_service_info.rate }}
          </div>
        </div>
        <div class="items" style="width:100%;">
          <el-button @click="submit()" type="primary" style="width:100%;">提交</el-button>
        </div>
      </div>

      <div class="right">
        {{ info.long_json }}
      </div>

    </div>
  </el-container>
</template>

<script>
import { findAllfans, getSmsFansConfigByAddConfig, reqFans, reqComments, reqSubscriptions, reqWebTraffic,getFansPrice } from '@/api/fensi.js'
import { Message } from 'element-ui'

export default {
  name: "index",
  data() {
    return {
      loading: true,
      type_id: "",
      server_id: "",
      types: [],
      services: [],
      current_service_info: {},
      info: {},
      value: '',
      page: {
        service_id: "", link: "", quantity: ""
      }
    }
  },
  methods: {
    changeQuantity(){
      this.page.service_id = this.current_service_info.fid;
      if(!this.page.link) {
        this.page.link = "/";
      }
      getFansPrice(this.page).then(res => {
        if(res.code!="200") {
          return;
        }
        this.current_service_info.rate = res.data;
        console.log(res.data);
      });
    },
    submit() {
      this.page.service_id = this.current_service_info.fid;
      console.log(this.page);
      if (!this.page.service_id) {
        Message({
          message: "请选择服务!",
          type: 'error',
          duration: 5 * 1000
        })
        return;
      }
      if (!this.page.link) {
        Message({
          message: "请输入链接!",
          type: 'error',
          duration: 5 * 1000
        })
        return;
      }
      if (!this.page.quantity) {
        Message({
          message: "请输入数量!",
          type: 'error',
          duration: 5 * 1000
        })
        return;
      }
      this.loading = true;
      if (this.current_service_info.type == "Default") {

        reqFans(this.page).then(res => {
          this.goOrder(res);
        });

      } else if (this.current_service_info.type == "Custom Comments") {

        reqComments(this.page).then(res => {
          this.goOrder(res);
        });

      } else if (this.current_service_info.type == "Subscriptions") {

        reqSubscriptions(this.page).then(res => {
          this.goOrder(res);
        });

      } else if (this.current_service_info.type == "reqWebTraffic") {

        reqWebTraffic(this.page).then(res => {
          this.goOrder(res);
        });

      }


      // Message({
      //   message: res.msg,
      //   type: 'success',
      //   duration: 5 * 1000
      // });

    },
    goOrder(res) {
      console.log(res);
      this.loading = false;
      if (res.code != "200") {
        Message({
          message: res.msg,
          type: 'error',
          duration: 5 * 1000
        })
        return;
      }
      this.$message({
        type: 'success',
        message: res.msg || '操作成功!'
      });
      setTimeout(() => {
        this.$router.push({ path: '/fensi/fensiOrder' })
      }, 500);
    },
    loadType(fn) {
      findAllfans().then(res => {
        this.types = res.data;
        if (fn) {
          fn();
        }
      })
    },
    changeType(e) {
      this.type_id = e;
      this.loadServer();
    },
    changeService(e) {
      this.server_id = e;
      let serverInfo = this.getServerInfo();
      this.current_service_info = serverInfo;
      console.log(serverInfo);
    },
    loadServer() {
      this.server_id = "";
      this.types.forEach(element => {
        if (element.fid == this.type_id) {
          this.services = element.children;
        }
      });
    },
    getServerInfo() {
      let service_info = null;
      this.services.forEach(element => {
        if (element.fid == this.server_id) {
          service_info = element;
        }
      });
      return service_info;
    },
    // current_service_info
  },
  created() {
  },
  mounted() {
    this.loadType(() => {
      let option = this.$route.query;
      this.type_id = option.parent_id;
      this.loadServer();
      this.server_id = option.fid;
      this.changeService(this.server_id);
      this.current_service_info.rate = 0.0;
    });
    getSmsFansConfigByAddConfig().then(res => {
      this.info = res.data
    })
  }
}
</script>

<style lang="scss" scoped>
.daili {
  &-container {
    margin: 30px;
    width: 100%;
    display: flex;

    .left {
      width: 53%;
      // border: 1px solid red;

      .items {
        width: 100%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-top: 10px;

        .label {
          color: #999;
          margin-right: 15px;
          width: 100px;
          text-align: right;
        }

        .value {
          width: 100%;
        }
      }
    }

    .right {
      // border: 1px solid red;
      width: 47%;
      padding-left: 30px;
    }
  }

  &-text {
    font-size: 30px;
    line-height: 46px;
  }
}
</style>
