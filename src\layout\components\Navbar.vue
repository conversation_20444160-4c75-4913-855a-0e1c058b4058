<template>
  <div class="navbar">
    <div class="left-menu">
      <div class="logo-container">
        <router-link to="/" class="logo-link">
          <img src="https://wpimg.wallstcn.com/69a1c46c-eb1c-4b46-8bd4-e9e686ef5251.png" class="logo-image" alt="Logo">
          <h1 class="logo-text">SocialAccTrade</h1>
        </router-link>
      </div>
    </div>
    
    <div class="right-menu">
      <div class="search-wrapper">
        <el-input
          placeholder="search"
          prefix-icon="el-icon-search"
          v-model="searchInput"
          class="search-input"
        >
        </el-input>
      </div>
      
      <el-tooltip content="通知" effect="dark" placement="bottom">
        <div class="right-menu-item">
          <i class="el-icon-bell"></i>
        </div>
      </el-tooltip>
      
      <el-dropdown trigger="click" class="avatar-container">
        <div class="avatar-wrapper">
          <div class="avatar-circle">N</div>
        </div>
        <el-dropdown-menu slot="dropdown" class="user-dropdown">
          <router-link to="/">
            <el-dropdown-item>首页</el-dropdown-item>
          </router-link>
          <el-dropdown-item divided @click.native="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      searchInput: ''
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar'
    ])
  },
  methods: {
    async logout() {
      await this.$store.dispatch('user/logout')
      this.$router.push(`/login?redirect=${this.$route.fullPath}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 60px;
  padding: 0 20px;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .left-menu {
    display: flex;
    align-items: center;
    
    .logo-container {
      .logo-link {
        text-decoration: none;
        display: flex;
        align-items: center;
        
        .logo-image {
          width: 32px;
          height: 32px;
          margin-right: 10px;
        }
        
        .logo-text {
          color: #051529;
          font-size: 20px;
          font-weight: bold;
          margin: 0;
        }
      }
    }
  }
  
  .right-menu {
    display: flex;
    align-items: center;
    
    .search-wrapper {
      margin-right: 20px;
      
      .search-input {
        width: 200px;
        
        :deep(.el-input__inner) {
          border-radius: 20px;
          background-color: #f0f2f5;
          border: none;
          height: 36px;
        }
      }
    }
    
    .right-menu-item {
      padding: 0 12px;
      font-size: 20px;
      color: #606266;
      cursor: pointer;
      
      &:hover {
        color: #409EFF;
      }
    }
    
    .avatar-container {
      margin-left: 20px;
      cursor: pointer;
      
      .avatar-wrapper {
        .avatar-circle {
          width: 36px;
          height: 36px;
          border-radius: 50%;
          background-color: #0d3b49;
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
          font-weight: bold;
        }
      }
    }
  }
}
</style>

