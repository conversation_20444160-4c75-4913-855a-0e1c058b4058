<template>
  <el-table
      :data="tableData"
      style="width: 100%">
    <el-table-column
        label="订单号"
        width="100"
        prop="d_order_id">
    </el-table-column>
    <el-table-column
        label="国家"
        width="150"
        prop="country_name">
    </el-table-column>
    <el-table-column
        label="服务名"
        width="150"
        prop="server_name">
    </el-table-column>

    <el-table-column
        label="手机号"
        width="150"
        prop="mobile">
    </el-table-column>
    <el-table-column
        label="验证码"
        width="100"
        prop="vcode">
    </el-table-column>
    <el-table-column
        label="价格"
        width="100"
        prop="price">
    </el-table-column>
    <el-table-column
        label="有效期起"
        width="170"
        prop="begin_date2">
      </el-table-column>
      <el-table-column
        label="有效期止"
        width="170"
        prop="end_date2">
    </el-table-column>
      <el-table-column
        label="创建时间"
        width="170"
        prop="add_time_format">
    </el-table-column>
  </el-table>
</template>

<script>
import { findMySmsOrders} from '@/api/jiema.js'

export default {
  data() {
    return {
      tableData: []
    }
  },
  methods:{
    load(){
       findMySmsOrders().then(res => {
        this.tableData = res.data.list;
      })
    }
  },
  mounted() {
    this.load();
  }
}
</script>

<style scoped>

</style>
