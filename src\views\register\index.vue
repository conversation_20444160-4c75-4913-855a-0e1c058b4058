<template>
  <div class="login-container">
    <div class="login-form-section">
      <div class="login-form-wrapper">
        <div class="login-header">
          <h1 class="title">注册账号</h1>
          <p class="subtitle">在 SocialAccTracks，我们为您提供安全、可靠的社交媒体和交易服务。</p>
        </div>
        
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" auto-complete="on">
          <div class="form-item-label">Email</div>
          <el-form-item prop="email">
            <el-input v-model="loginForm.email" placeholder="<EMAIL>" type="text" />
          </el-form-item>
          
          <div class="form-item-label">Verify</div>
          <el-form-item prop="vcode">
            <div class="verification-container">
              <el-input v-model="loginForm.vcode" placeholder="Verification Code" class="verification-input" />
              <el-button type="primary" class="verification-button" @click="changeVCode">发送验证码</el-button>
            </div>
          </el-form-item>
          
          <div class="form-item-label">Password</div>
          <el-form-item prop="password">
            <el-input v-model="loginForm.password" placeholder="At least 8 characters" type="password" />
          </el-form-item>
          
          <el-button :loading="loading" type="primary" class="submit-button" @click.native.prevent="handleLogin">
            注册
          </el-button>
          
          <div class="register-link">
            已有账户？<router-link to="/login">立即登录</router-link>
          </div>
        </el-form>
      </div>
      
      <div class="footer">
        © {{ new Date().getFullYear() }} 版权所有
      </div>
    </div>
    
    <!-- <div class="background-section"></div> -->
    <div class="background-container">
      <div class="background-section"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Register',
  data() {
    const validateEmail = (rule, value, callback) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(value)) {
        callback(new Error('请输入有效的邮箱地址'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length < 8) {
        callback(new Error('密码不能少于8个字符'))
      } else {
        callback()
      }
    }
    return {
      loginForm: {
        email: '',
        password: '',
        vcode: ''
      },
      loginRules: {
        email: [{ required: true, trigger: 'blur', validator: validateEmail }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }],
        vcode: [{ required: true, trigger: 'blur', message: '请输入验证码' }]
      },
      loading: false
    }
  },
  methods: {
    changeVCode() {
      this.$message({
        message: '验证码已发送',
        type: 'success'
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.$router.push('/')
          }, 1500)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  display: flex;
  min-height: 100vh;
  width: 100%;
  
  .login-form-section {
    width: 55%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 40px 24px;
    
    .login-form-wrapper {
      width: 100%;
      max-width: 380px;
      margin: 145px auto 0;
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      
      .login-header {
        margin-bottom: 30px;
        
        .title {
          font-size: 28px;
          font-weight: 600;
          color: #000;
          margin-bottom: 10px;
        }
        
        .subtitle {
          font-size: 14px;
          color: #000;
          line-height: 1.5;
        }
      }
      
      .login-form {
        .form-item-label {
          font-weight: 500;
          font-size: 14px;
          color: #333;
          margin-bottom: 8px;
        }
        .verification-container {
          display: flex;
          
          .verification-input {
            flex: 1;
          }
          
          .verification-button {
            margin-left: 10px;
            width: 120px;
            background-color: #0d3b49;
            border-color: #0d3b49;
          }
        }
        
        .submit-button {
          width: 100%;
          height: 44px;
          background-color: #0d3b49;
          border-color: #0d3b49;
          font-size: 16px;
          font-weight: 500;
          
          &:hover, &:focus {
            background-color: #165a6d;
            border-color: #165a6d;
          }
        }
        .register-link {
          text-align: center;
          margin-top: 50px;
          font-size: 12px;
          color: #838C9A ;
          
          a {
            color: #0d3b49;
            text-decoration: none;
            font-weight: 500;
            
            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
    }
    
    .footer {
      width: 100%;
      text-align: center;
      color: #999;
      font-size: 12px;
      margin-top: 20px;
    }
  }
  .background-container {
    width: 45%;
    overflow: hidden;
    padding: 26px 65px 27px 0;
    .background-section {
      width: 100%;
      height: 100%;
      background-image: url('~@/assets/background.png');
      background-size: cover;
      background-position: center;
      border-radius: 1%;
    }
  }
}
</style>