<template>
  <div class="app-wrapper">
    <!-- 顶部导航栏 -->
    <navbar class="navbar-container" />

    <!-- 下方内容区域 -->
    <div class="content-wrapper">
      <sidebar class="sidebar-container" />
      <app-main class="main-container" />
    </div>
  </div>
</template>

<script>
import { Navbar, Sidebar, AppMain } from './components'
import ResizeMixin from './mixin/ResizeHandler'

export default {
  name: 'Layout',
  components: {
    Navbar,
    Sidebar,
    AppMain
  },
  mixins: [ResizeMixin]
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/mixin.scss";
  @import "~@/styles/variables.scss";

  .app-wrapper {
    @include clearfix;
    position: relative;
    height: 100vh;
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .navbar-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1002;
    height: 60px;
  }

  .content-wrapper {
    margin-top: 60px; // 为顶部导航栏留出空间
    height: calc(100vh - 60px);
    display: flex;
  }

  .sidebar-container {
    width: 200px;
    background-color: #051529;
    height: 100%;
    overflow-y: auto;
    flex-shrink: 0;
  }

  .main-container {
    flex: 1;
    background-color: #f5f7fa;
    overflow-y: auto;
  }
</style>
