<template>
  <div class="login-container">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" auto-complete="on"
      label-position="left">

      <div class="title-container">
        <h3 class="title">Login Form</h3>
      </div>

      <el-form-item prop="user_name">
        <span class="svg-container">
          <svg-icon icon-class="user" />
        </span>
        <el-input ref="user_name" v-model="loginForm.user_name" placeholder="user_name" name="user_name" type="text"
          tabindex="1" auto-complete="on" />
      </el-form-item>

      <el-form-item prop="password">
        <span class="svg-container">
          <svg-icon icon-class="password" />
        </span>
        <el-input :key="passwordType" ref="password" v-model="loginForm.password" :type="passwordType"
          placeholder="Password" name="password" tabindex="2" auto-complete="on" @keyup.enter.native="handleLogin" />
        <span class="show-pwd" @click="showPwd">
          <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
        </span>
      </el-form-item>


      <el-form-item prop="vcode">
        <div style="display: flex;">
          <div style="width:80%">
            <span class="svg-container">
              <svg-icon icon-class="user" />
            </span>
            <el-input ref="vcode" v-model="loginForm.vcode" placeholder="vcode" name="vcode" type="text" tabindex="1"
              auto-complete="on" />
          </div>
          <div class="v_code" @click="changeVCode()">
            <img class="img" :src="v_code_url" alt="" />
          </div>
        </div>
      </el-form-item>


      <el-button :loading="loading" type="primary" style="width:100%;margin-bottom:30px;"
        @click.native.prevent="handleLogin">Login</el-button>

      <div class="tips">
        <span style="margin-right:20px;">user_name: admin</span>
        <span> password: any</span>
      </div>

    </el-form>
  </div>
</template>

<script>
import { login, getVerificationCode } from '@/api/user.js'
import { validuser_name } from '@/utils/validate'
import { setToken } from '@/utils/auth'

export default {
  name: 'Login',
  data() {
    const validateuser_name = (rule, value, callback) => {
      if (!validuser_name(value)) {
        callback(new Error('Please enter the correct user name'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error('The password can not be less than 6 digits'))
      } else {
        callback()
      }
    }
    return {
      t: "",
      v_code_url: "",
      loginForm: {
        user_name: '',
        password: '',
        vcode: ""
      },
      loginRules: {
        user_name: [{ required: true, trigger: 'blur', validator: validateuser_name }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }],
        // vcode: [{ required: true, trigger: 'blur', validator: validatePassword }],
      },
      loading: false,
      passwordType: 'password',
      redirect: undefined
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  created() {
    this.changeVCode();
  },
  methods: {
    changeVCode() {
      getVerificationCode({ t: this.guid() }).then((r) => {
        let res = r.response;
        var result = JSON.parse(res.headers.t);
        this.t = result.t;
        this.v_code_url = result.img_url;
      });
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      let params = this.loginForm;
      params.t = this.t;
      this.loading = true
      login(params).then(res => {
        if (res.data.token) {
          console.log("登录成功`！");
          console.log(res.data.token);
          // localStorage.setItem("vue_admin_template_token", res.data.token);
          setToken(res.data.token);
          this.$router.push({ path: this.redirect || '/' })
        }
        this.loading = false;
        // this.tableData = res.data;
      }).catch(() => {
        this.loading = false;
      })
      // this.$refs.loginForm.validate(valid => {
      //   if (valid) {
      //     this.loading = true
      //     this.$store.dispatch('user/login', this.loginForm).then(() => {
      //       this.$router.push({ path: this.redirect || '/' })
      //       this.loading = false
      //     }).catch(() => {
      //       this.loading = false
      //     })
      //   } else {
      //     console.log('error submit!!')
      //     return false
      //   }
      // })
    },
    S4() {
      return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    },
    guid() {
      return (this.S4() + this.S4() + "" + this.S4() + "" + this.S4() + "" + this.S4() + "" +
        this.S4() + this.S4() + this.S4());
    },
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: #283443;
$light_gray: #fff;
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      height: 47px;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
  }
}
</style>

<style lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;

.login-container {
  min-height: 100%;
  width: 100%;
  background-color: $bg;
  overflow: hidden;

  .login-form {
    position: relative;
    width: 520px;
    max-width: 100%;
    padding: 160px 35px 0;
    margin: 0 auto;
    overflow: hidden;
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 26px;
      color: $light_gray;
      margin: 0px auto 40px auto;
      text-align: center;
      font-weight: bold;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }
}

.v_code {
  // position: absolute;
  // width: 100%;
  // text-align: right;
  // border: 1px solid red;
  margin-left: -20px;

  .img {
    position: absolute;
    width: 100px;
    height: 40px;
    margin: 5px 5px 0 0;

  }
}
</style>
