<template>
  <div class="daili-container">
    <div>
      <el-table
          :data="tableData"
          style="width: 100%">
        <el-table-column
            prop="order_id"
            label="ID"
            width="100">
        </el-table-column>
        <el-table-column
            width="150"
            prop="add_time_format"
            label="日期"
        >
        </el-table-column>
        <el-table-column
            prop="link"
            label="链接"
            width="200">
        </el-table-column>
        <el-table-column
            prop="charge"
            label="费用"
            width="130">
        </el-table-column>
        <el-table-column
            prop="start_count"
            label="开始计数"
            width="130">
        </el-table-column>
        <el-table-column
            prop="remains"
            label="数量"
            width="130">
        </el-table-column>
        <el-table-column
            prop="status_name"
            label="状态"
            width="130">
        </el-table-column>
        <el-table-column
            prop="server_name"
            label="服务">
          <template slot-scope="scope">
            {{scope.row.name}}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import {findMyFansOrders} from '@/api/fensi.js'
export default {
  name: "index",
  data() {
    return {
      info: null,
      tableData: [],
      form: {
        type: 1,
        bb: 1,
        guojia: 1,
        num: 1,
        date: 1
      }
    }
  },
  methods: {

  },
  mounted() {
    findMyFansOrders().then(res => {
      console.log(res);
      this.tableData = res.data.list;
    })
  }
}
</script>

<style lang="scss" scoped>
.daili {
  &-container {
    margin: 30px;
  }
  &-text {
    font-size: 30px;
    line-height: 46px;
  }
}
</style>
