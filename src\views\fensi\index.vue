<template>
  <div class="daili-container">
    <div class="header">
      <div class="id">ID</div>
      <div class="server">服务</div>
      <div class="qprice">每千人价格</div>
      <div class="min_dg">最小订购数</div>
      <div class="max_dg">最大订购数</div>
      <!-- <div class="pj_time">平均时间</div> -->
      <div class="memo">描述</div>
    </div>

    <div v-for="(item, tindex) in tableData" :key="tindex" class="details">
      <div class="detail">{{ item.category }}</div>
      <div class="body" v-for="(children, cindex) in item.children" :key="cindex">
        <div class="id">{{ children.service_id }}</div>
        <div class="server">{{ children.name }}</div>
        <div class="qprice">¥{{ children.rate }}</div>
        <div class="min_dg">{{ children.min }}</div>
        <div class="max_dg">{{ children.max }}</div>
        <!-- <div class="pj_time">平均时间</div> -->
        <div class="memo">
          <div class="btn" @click="selectDetail(children)">查看</div>
        </div>
      </div>
    </div>

    <el-dialog title="" :visible.sync="centerDialogVisible" width="30%" center>
      <p v-html="pre_html">
      </p>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="centerDialogVisible = false">取 消</el-button> -->
        <el-button type="primary" @click="createOrder()">创建订单</el-button>
      </span>
    </el-dialog>


  </div>
</template>

<script>
import { findAllfans } from '@/api/fensi.js'
export default {
  name: "index",
  data() {
    return {
      centerDialogVisible: false,
      info: null,
      pre_html: "",
      tableData: [],
      current_select: null
    }
  },
  methods: {
    selectDetail(item) {
      this.pre_html = item.select_long_json;
      this.centerDialogVisible = true;
      this.current_select = item;
    },
    createOrder() {
      console.log(this.current_select);
      this.centerDialogVisible = false;
      this.$router.push({ path: '/fensi/fensiDetail?fid=' + this.current_select.fid + "&parent_id=" + this.current_select.parent_id })
    },
  },
  mounted() {
    // /fensi/fensiDetail
    findAllfans().then(res => {
      this.tableData = res.data
    })
  }
}
</script>

<style lang="scss" scoped>
.daili {
  &-container {
    margin: 30px;

    .header {
      width: 100%;
      display: flex;
      font-weight: 900;
      background-color: #D2E4FA;
      padding: 15px;
      border-radius: 10px;

      .id {
        width: 100px;
      }

      .server {
        flex: 1;
      }

      .qprice {
        width: 150px;
      }

      .min_dg {
        width: 150px;
      }

      .max_dg {
        width: 150px;
      }

      .pj_time {
        width: 150px;
      }

      .memo {
        width: 100px;
      }
    }

    .details {
      .detail {
        // margin: 20px 10px 20px 0px;
        font-weight: 900;
        border-bottom: 1px solid #1e79e42b;
        height: 50px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        // padding-bottom: 10px;
      }

      .body {
        width: 100%;
        display: flex;
        border-bottom: 1px solid #1e79e42b;
        height: 50px;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .id {
          width: 100px;
        }

        .server {
          flex: 1;
        }

        .qprice {
          width: 150px;
        }

        .min_dg {
          width: 150px;
        }

        .max_dg {
          width: 150px;
        }

        .pj_time {
          width: 150px;
        }

        .memo {
          width: 100px;

          .btn {
            background-color: #1B6DCD;
            width: 55px;
            height: 35px;
            color: #FFF;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: 900;
            border-radius: 10px;
            cursor: pointer;
          }
        }
      }
    }


  }

  &-text {
    font-size: 30px;
    line-height: 46px;
  }
}
</style>
