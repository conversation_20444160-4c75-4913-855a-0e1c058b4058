<template>
  <div class="ip-container">
    <div class="page-header">
      <h2 class="page-title">IP定型</h2>
      
      <div class="tab-container">
        <el-button-group>
          <el-button type="primary" class="tab-button active">私人IP</el-button>
          <el-button class="tab-button">共享</el-button>
        </el-button-group>
      </div>
      
      <div class="info-box">
        <p>私人IP: 此类代理服务为您提供独占，并保证您是此IP 唯一的一个"拥有者"。私人IP意味为您提供IP的独占，并保证您是此IP 唯一的一个"拥有者"。</p>
      </div>
    </div>
    
    <div class="form-section">
      <div class="form-group">
        <div class="form-label">IP版本</div>
        <el-button-group>
          <el-button type="primary" class="version-button active" @click="form.bb = '4'">IPv4</el-button>
          <el-button class="version-button" @click="form.bb = '6'">IPv6</el-button>
        </el-button-group>
      </div>
      
      <div class="form-group">
        <div class="form-label">国家</div>
        <el-select v-model="form.guojia" placeholder="Please select" class="form-select">
          <el-option
            v-for="item in info.countrys"
            :key="item.country"
            :label="item.country_name"
            :value="item.country">
          </el-option>
        </el-select>
      </div>
      
      <div class="form-group">
        <div class="form-label">数量</div>
        <div class="quantity-selector">
          <el-button icon="el-icon-minus" class="quantity-button" @click="decreaseQuantity"></el-button>
          <div class="quantity-display">{{ form.num }}</div>
          <el-button icon="el-icon-plus" class="quantity-button" @click="increaseQuantity"></el-button>
        </div>
      </div>
      
      <div class="form-group">
        <div class="form-label">天数</div>
        <el-select v-model="form.date" placeholder="Please select" class="form-select" @change="getPrice">
          <el-option label="30天" value="30"></el-option>
          <el-option label="60天" value="60"></el-option>
          <el-option label="90天" value="90"></el-option>
        </el-select>
      </div>
      
      <div class="form-group">
        <div class="form-label">结算金额（元）</div>
        <div class="price-display">{{ form.amount.toFixed(2) }}</div>
      </div>
      
      <div class="form-actions">
        <el-button type="primary" class="submit-button" @click="createOrderNow" :loading="loading">立即下单</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { findProxyHome, getPrice, createOrder, findProxyCountry } from '@/api/daili.js'

export default {
  name: "IPConfig",
  data() {
    return {
      loading: false,
      info: {
        countrys: []
      },
      form: {
        type: "dedicated",
        bb: "4",
        guojia: "",
        num: 1,
        date: "30",
        amount: 0
      }
    }
  },
  mounted() {
    findProxyHome().then(res => {
      this.info = res.data;
      console.log(this.info);
      setTimeout(() => {
        this.getPrice();
        setTimeout(() => {
          this.findCountry();
        }, 100);
      }, 100);
    })
  },
  methods: {
    findCountry(){
      setTimeout(() => {
        let params = {
            type_id: this.form.type,
            ip_version:this.form.bb
        };
        findProxyCountry(params).then(res => {
          this.form.guojia = "";
          this.$set(this.info, 'countrys', res.data.countrys)
          // this.info.countrys = res.data.countrys;
          console.log(this.info.countrys);
          this.loading = false;
          this.getPrice();
        }).catch(err => {
          this.loading = false;
        })
      }, 100);
    },
    getPrice() {
      let params = {
        type: this.form.type,
        ip_version: this.form.bb,
        country: this.form.guojia,
        quantity: this.form.num,
        period: this.form.date
      };
      if (!params.country) {
        return;
      }
      this.loading = true;
      getPrice(params).then(res => {
        console.log(res.data.amount);
        this.form.amount = res.data.amount || 0;
        this.loading = false;
      }).catch(err => {
        this.loading = false;
      })
    },
    increaseQuantity() {
      this.form.num++;
      this.getPrice();
    },
    decreaseQuantity() {
      if (this.form.num > 1) {
        this.form.num--;
        this.getPrice();
      }
    },
    createOrderNow() {
      let params = {
        type: this.form.type,
        ip_version: this.form.bb,
        country: this.form.guojia,
        quantity: this.form.num,
        period: this.form.date
      };
      if (!params.country) {
        return;
      }
      console.log("getToken", getToken());
      this.loading = true;
      createOrder(params).then(res => {
        this.loading = false;
        console.log(res);
        if (res.code != "200") {
          Message({
            message: res.msg,
            type: 'error',
            duration: 5 * 1000
          })
          return;
        }
        Message({
          message: res.msg,
          type: 'success',
          duration: 5 * 1000
        });
        setTimeout(() => {
          this.$router.push({ path: '/daili/dailiOrder' })
        }, 1000);
      }).catch(err => {
        console.log(err);
        this.loading = false;
      });

    }
  }
}
</script>

<style lang="scss" scoped>
.ip-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  
  .page-header {
    margin-bottom: 20px;
    
    .page-title {
      font-size: 18px;
      font-weight: 500;
      color: #333;
      margin-bottom: 15px;
    }
    
    .tab-container {
      margin-bottom: 15px;
      
      .tab-button {
        padding: 8px 20px;
        font-size: 14px;
        
        &.active {
          background-color: #0d3b49;
          border-color: #0d3b49;
        }
      }
    }
    
    .info-box {
      background-color: #f9f9f9;
      border-radius: 4px;
      padding: 15px;
      margin-bottom: 20px;
      
      p {
        margin: 0;
        font-size: 14px;
        color: #666;
        line-height: 1.5;
      }
    }
  }
  
  .form-section {
    .form-group {
      margin-bottom: 20px;
      
      .form-label {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 10px;
      }
      
      .version-button {
        padding: 8px 20px;
        font-size: 14px;
        
        &.active {
          background-color: #0d3b49;
          border-color: #0d3b49;
        }
      }
      
      .form-select {
        width: 100%;
        max-width: 300px;
      }
      
      .quantity-selector {
        display: flex;
        align-items: center;
        max-width: 150px;
        
        .quantity-button {
          padding: 8px;
          background-color: #f5f5f5;
          border-color: #dcdfe6;
        }
        
        .quantity-display {
          flex: 1;
          text-align: center;
          padding: 8px 15px;
          background-color: #f5f5f5;
          border: 1px solid #dcdfe6;
          border-left: none;
          border-right: none;
          font-size: 14px;
        }
      }
      
      .price-display {
        font-size: 24px;
        font-weight: 500;
        color: #f56c6c;
      }
    }
    
    .form-actions {
      margin-top: 30px;
      
      .submit-button {
        padding: 10px 25px;
        font-size: 14px;
        background-color: #0d3b49;
        border-color: #0d3b49;
        
        &:hover, &:focus {
          background-color: #165a6d;
          border-color: #165a6d;
        }
      }
    }
  }
}

// 覆盖 Element UI 样式
:deep(.el-select .el-input__inner) {
  border-radius: 4px;
}

:deep(.el-button--primary) {
  background-color: #0d3b49;
  border-color: #0d3b49;
  
  &:hover, &:focus {
    background-color: #165a6d;
    border-color: #165a6d;
  }
}
</style>