import request from '@/utils/request'

export function findProxyHome(params) {
  return request({
    url: '/proxy/findProxyHome',
    method: 'get',
    params
  })
}

export function findMyProxy(params) {
  return request({
    url: '/proxy/findMyProxy',
    method: 'get',
    params
  })
}

export function getPrice(params) {
  return request({
    url: '/proxy/getPrice',
    method: 'get',
    params
  })
}

export function createOrder(params) {
  return request({
    url: '/proxy/createOrder',
    method: 'get',
    params
  })
}

export function xdMyProxy(params) {
  return request({
    url: '/proxy/xdMyProxy',
    method: 'get',
    params
  })
}

export function findProxyCountry(params) {
  return request({
    url: '/proxy/findProxyCountry',
    method: 'get',
    params
  })
}